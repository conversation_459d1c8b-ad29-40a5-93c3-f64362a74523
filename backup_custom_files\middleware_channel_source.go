package middleware

import (
	"github.com/gin-gonic/gin"
	"one-api/common"
	"one-api/model"
)

// ChannelSourceMiddleware 捕获GET请求中的_channel参数，并记录到数据库
func ChannelSourceMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只处理GET请求
		if c.Request.Method == "GET" {
			// 检查是否存在_channel参数
			channelName := c.Query("_channel")
			if channelName != "" {
				// 异步记录，不阻塞请求
				go func() {
					err := model.RecordChannelSource(channelName)
					if err != nil {
						common.SysError("failed to record channel source: " + err.Error())
					}
				}()
			}
		}

		// 继续处理请求
		c.Next()
	}
}