package middleware

import (
	"errors"
	"fmt"
	"net/http"
	"one-api/common"
	"one-api/constant"
	"one-api/dto"
	"one-api/model"
	relayconstant "one-api/relay/constant"
	"one-api/service"
	"one-api/setting"
	"sort" // Added for sorting GroupPriorities
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type ModelRequest struct {
	Model string `json:"model"`
}

func Distribute() func(c *gin.Context) {
	return func(c *gin.Context) {
		allowIpsMap := c.GetStringMap("allow_ips")
		if len(allowIpsMap) != 0 {
			clientIp := c.ClientIP()
			if _, ok := allowIpsMap[clientIp]; !ok {
				abortWithOpenAiMessage(c, http.StatusForbidden, "您的 IP 不在令牌允许访问的列表中")
				return
			}
		}
		var channel *model.Channel
		channelId, ok := c.Get("specific_channel_id")
		modelRequest, shouldSelectChannel, err := getModelRequest(c)
		if err != nil {
			abortWithOpenAiMessage(c, http.StatusBadRequest, "Invalid request, "+err.Error())
			return
		}

		// Get Token object and parse GroupPriorities
		tokenKey := c.GetString("token_key") // Assuming token key is stored by TokenAuth
		var currentToken *model.Token
		var tokenLoadError error

		if tokenKey != "" {
			currentToken, tokenLoadError = model.GetTokenByKey(tokenKey, false)
			if tokenLoadError != nil {
				common.SysError("Distributor: Failed to get token by key '" + tokenKey + "': " + tokenLoadError.Error())
				// If token cannot be loaded, it's a critical issue for group-based routing.
				// However, some requests might not need a channel if specific_channel_id is set.
				// For now, log and let it proceed; channel selection logic will fail if needed.
			} else {
				if err := currentToken.ParseGroupPriorities(); err != nil {
					common.SysError("Distributor: Failed to parse group priorities for token " + strconv.Itoa(currentToken.Id) + ": " + err.Error())
				}
			}
		}

		userGroup := c.GetString(constant.ContextKeyUserGroup) // User's default group
		tokenGroupString := c.GetString("token_group")         // Old single group string from token's "group" field
		effectiveGroup := userGroup                            // Initialize effectiveGroup with user's default group

		if ok { // This 'ok' is from: channelId, ok := c.Get("specific_channel_id")
			// Specific channel ID was provided in the request, use it directly
			id, err := strconv.Atoi(channelId.(string))
			if err != nil {
				abortWithOpenAiMessage(c, http.StatusBadRequest, "无效的渠道 Id")
				return
			}
			channel, err = model.GetChannelById(id, true)
			if err != nil {
				abortWithOpenAiMessage(c, http.StatusBadRequest, "无效的渠道 Id")
				return
			}
			if channel.Status != common.ChannelStatusEnabled {
				abortWithOpenAiMessage(c, http.StatusForbidden, "该渠道已被禁用")
				return
			}
			// If specific channel is used, its group should be the effective group
			// However, channel itself doesn't store which group it belongs to in a way that's directly usable here for 'effectiveGroup'
			// The 'group' context key is mainly for quota calculation based on group ratio.
			// If a specific channel is chosen, the group ratio applied might be the channel's own, or default.
			// For now, if specific channel is chosen, we might not need to set token-derived group.
			// Let's assume the original userGroup or a group associated with the channel (if any) would be used.
			// This part might need further clarification if specific channel selection should also override group for quota.
			// For now, let effectiveGroup remain userGroup if specific channel is chosen.
		} else {
			// No specific channel ID, proceed with group-based selection logic
			modelLimitEnable := c.GetBool("token_model_limit_enabled")
			if modelLimitEnable {
				s, ok := c.Get("token_model_limit")
				var tokenModelLimit map[string]bool
				if ok {
					tokenModelLimit = s.(map[string]bool)
				} else {
					tokenModelLimit = map[string]bool{}
				}
				if tokenModelLimit != nil {
					if _, ok := tokenModelLimit[modelRequest.Model]; !ok {
						abortWithOpenAiMessage(c, http.StatusForbidden, "该令牌无权访问模型 "+modelRequest.Model)
						return
					}
				} else {
					abortWithOpenAiMessage(c, http.StatusForbidden, "该令牌无权访问任何模型")
					return
				}
			}

			if shouldSelectChannel {
				var foundChannelUsingPriorityLogic bool
				// 1. Try with token.GroupPriorities
				if currentToken != nil && len(currentToken.GroupPriorities) > 0 {
					sortedGroupPriorities := make([]model.GroupPriority, len(currentToken.GroupPriorities))
					copy(sortedGroupPriorities, currentToken.GroupPriorities)
					sort.SliceStable(sortedGroupPriorities, func(i, j int) bool {
						return sortedGroupPriorities[i].Priority < sortedGroupPriorities[j].Priority
					})

					for _, gp := range sortedGroupPriorities {
						groupName := gp.Name
						// Validate groupName
						if _, groupExists := setting.GetUserUsableGroups(userGroup)[groupName]; !groupExists {
							common.LogInfo(c, fmt.Sprintf("令牌配置的优先分组 %s 不可用或已被禁用, 跳过", groupName))
							continue
						}
						if !setting.ContainsGroupRatio(groupName) {
							common.LogInfo(c, fmt.Sprintf("令牌配置的优先分组 %s 的倍率未配置或已被弃用, 跳过", groupName))
							continue
						}

						tempChannel, tempErr := model.CacheGetRandomSatisfiedChannel(groupName, modelRequest.Model, 0)
						if tempErr == nil && tempChannel != nil {
							channel = tempChannel
							effectiveGroup = groupName
							foundChannelUsingPriorityLogic = true
							common.LogInfo(c, fmt.Sprintf("通过令牌优先分组 %s (优先级 %d) 找到可用渠道 %d for model %s", groupName, gp.Priority, channel.Id, modelRequest.Model))
							break // Found a channel, exit loop
						}
						if tempErr != nil {
							common.LogInfo(c, fmt.Sprintf("在优先分组 %s 中为模型 %s 查找渠道失败: %s", groupName, modelRequest.Model, tempErr.Error()))
						}
					}
				}

				// 2. Fallback to tokenGroupString (old field) if no channel found yet
				if !foundChannelUsingPriorityLogic && tokenGroupString != "" {
					isValidOldTokenGroup := true
					if _, groupExists := setting.GetUserUsableGroups(userGroup)[tokenGroupString]; !groupExists {
						common.LogInfo(c, fmt.Sprintf("令牌旧分组字段 %s 不可用或已被禁用", tokenGroupString))
						isValidOldTokenGroup = false
					}
					if isValidOldTokenGroup && !setting.ContainsGroupRatio(tokenGroupString) {
						common.LogInfo(c, fmt.Sprintf("令牌旧分组字段 %s 的倍率未配置或已被弃用", tokenGroupString))
						isValidOldTokenGroup = false
					}

					if isValidOldTokenGroup {
						common.LogInfo(c, fmt.Sprintf("尝试使用令牌旧分组字段 %s 查找渠道 for model %s", tokenGroupString, modelRequest.Model))
						tempChannel, tempErr := model.CacheGetRandomSatisfiedChannel(tokenGroupString, modelRequest.Model, 0)
						if tempErr == nil && tempChannel != nil {
							channel = tempChannel
							effectiveGroup = tokenGroupString
							foundChannelUsingPriorityLogic = true // Mark as found to prevent userGroup fallback if this succeeds
							common.LogInfo(c, fmt.Sprintf("通过令牌旧分组 %s 找到可用渠道 %d for model %s", tokenGroupString, channel.Id, modelRequest.Model))
						} else if tempErr != nil {
							common.LogInfo(c, fmt.Sprintf("在旧分组 %s 中为模型 %s 查找渠道失败: %s", tokenGroupString, modelRequest.Model, tempErr.Error()))
						}
					}
				}

				// 3. Fallback to user's default group if still no channel found
				if !foundChannelUsingPriorityLogic {
					common.LogInfo(c, fmt.Sprintf("尝试使用用户默认分组 %s 查找渠道 for model %s", userGroup, modelRequest.Model))
					// Validate userGroup (though typically it should be valid, check ratio)
					if !setting.ContainsGroupRatio(userGroup) && userGroup != "default" { // Allow 'default' even if not explicitly in ratios? Or should default always have a ratio?
						// This case might indicate a configuration issue if userGroup is not 'default' and has no ratio.
						// For now, proceed, CacheGetRandomSatisfiedChannel might handle groups without specific ratios by using a default.
						common.LogInfo(c, fmt.Sprintf("用户默认分组 %s 的倍率可能未配置", userGroup))
					}
					tempChannel, tempErr := model.CacheGetRandomSatisfiedChannel(userGroup, modelRequest.Model, 0)
					if tempErr == nil && tempChannel != nil {
						channel = tempChannel
						effectiveGroup = userGroup // effectiveGroup was already userGroup by default
						common.LogInfo(c, fmt.Sprintf("通过用户默认分组 %s 找到可用渠道 %d for model %s", userGroup, channel.Id, modelRequest.Model))
					} else if tempErr != nil {
						common.LogInfo(c, fmt.Sprintf("在用户默认分组 %s 中为模型 %s 查找渠道失败: %s", userGroup, modelRequest.Model, tempErr.Error()))
					}
				}

				// Final check if a channel was found
				if channel == nil {
					message := fmt.Sprintf("在用户分组 %s 以及令牌配置的所有分组下均未找到模型 %s 的可用渠道", userGroup, modelRequest.Model)
					if currentToken != nil && len(currentToken.GroupPriorities) > 0 {
						gps := make([]string, len(currentToken.GroupPriorities))
						for i, gp := range currentToken.GroupPriorities {
							gps[i] = fmt.Sprintf("%s(P%d)", gp.Name, gp.Priority)
						}
						message = fmt.Sprintf("令牌配置的优先分组 (%s), 旧分组 (%s), 以及用户默认分组 (%s) 均未找到模型 %s 的可用渠道",
							strings.Join(gps, ", "), tokenGroupString, userGroup, modelRequest.Model)
					} else if tokenGroupString != "" {
						message = fmt.Sprintf("令牌旧分组 (%s) 以及用户默认分组 (%s) 均未找到模型 %s 的可用渠道",
							tokenGroupString, userGroup, modelRequest.Model)
					}
					abortWithOpenAiMessage(c, http.StatusServiceUnavailable, message)
					return
				}
			}
		}
		c.Set("group", effectiveGroup) // Set the final effective group to context
		c.Set(constant.ContextKeyRequestStartTime, time.Now())
		SetupContextForSelectedChannel(c, channel, modelRequest.Model)
		c.Next()
	}
}

func getModelRequest(c *gin.Context) (*ModelRequest, bool, error) {
	var modelRequest ModelRequest
	shouldSelectChannel := true
	var err error
	if strings.Contains(c.Request.URL.Path, "/mj/") {
		relayMode := relayconstant.Path2RelayModeMidjourney(c.Request.URL.Path)
		if relayMode == relayconstant.RelayModeMidjourneyTaskFetch ||
			relayMode == relayconstant.RelayModeMidjourneyTaskFetchByCondition ||
			relayMode == relayconstant.RelayModeMidjourneyNotify ||
			relayMode == relayconstant.RelayModeMidjourneyTaskImageSeed {
			shouldSelectChannel = false
		} else {
			midjourneyRequest := dto.MidjourneyRequest{}
			err = common.UnmarshalBodyReusable(c, &midjourneyRequest)
			if err != nil {
				return nil, false, err
			}
			midjourneyModel, mjErr, success := service.GetMjRequestModel(relayMode, &midjourneyRequest)
			if mjErr != nil {
				return nil, false, fmt.Errorf(mjErr.Description)
			}
			if midjourneyModel == "" {
				if !success {
					return nil, false, fmt.Errorf("无效的请求, 无法解析模型")
				} else {
					// task fetch, task fetch by condition, notify
					shouldSelectChannel = false
				}
			}
			modelRequest.Model = midjourneyModel
		}
		c.Set("relay_mode", relayMode)
	} else if strings.Contains(c.Request.URL.Path, "/suno/") {
		relayMode := relayconstant.Path2RelaySuno(c.Request.Method, c.Request.URL.Path)
		if relayMode == relayconstant.RelayModeSunoFetch ||
			relayMode == relayconstant.RelayModeSunoFetchByID {
			shouldSelectChannel = false
		} else {
			modelName := service.CoverTaskActionToModelName(constant.TaskPlatformSuno, c.Param("action"))
			modelRequest.Model = modelName
		}
		c.Set("platform", string(constant.TaskPlatformSuno))
		c.Set("relay_mode", relayMode)
	} else if strings.HasPrefix(c.Request.URL.Path, "/v1beta/models/") {
		// Gemini API 路径处理: /v1beta/models/gemini-2.0-flash:generateContent
		relayMode := relayconstant.RelayModeGemini
		modelName := extractModelNameFromGeminiPath(c.Request.URL.Path)
		if modelName != "" {
			modelRequest.Model = modelName
		}
		c.Set("relay_mode", relayMode)
	} else if !strings.HasPrefix(c.Request.URL.Path, "/v1/audio/transcriptions") && !strings.HasPrefix(c.Request.URL.Path, "/v1/images/edits") {
		err = common.UnmarshalBodyReusable(c, &modelRequest)
	}
	if err != nil {
		return nil, false, errors.New("无效的请求, " + err.Error())
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/realtime") {
		//wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01
		modelRequest.Model = c.Query("model")
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/moderations") {
		if modelRequest.Model == "" {
			modelRequest.Model = "text-moderation-stable"
		}
	}
	if strings.HasSuffix(c.Request.URL.Path, "embeddings") {
		if modelRequest.Model == "" {
			modelRequest.Model = c.Param("model")
		}
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/images/generations") {
		modelRequest.Model = common.GetStringIfEmpty(modelRequest.Model, "dall-e")
	} else if strings.HasPrefix(c.Request.URL.Path, "/v1/images/edits") {
		modelRequest.Model = common.GetStringIfEmpty(c.PostForm("model"), "gpt-image-1")
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/audio") {
		relayMode := relayconstant.RelayModeAudioSpeech
		if strings.HasPrefix(c.Request.URL.Path, "/v1/audio/speech") {
			modelRequest.Model = common.GetStringIfEmpty(modelRequest.Model, "tts-1")
		} else if strings.HasPrefix(c.Request.URL.Path, "/v1/audio/translations") {
			modelRequest.Model = common.GetStringIfEmpty(modelRequest.Model, c.PostForm("model"))
			modelRequest.Model = common.GetStringIfEmpty(modelRequest.Model, "whisper-1")
			relayMode = relayconstant.RelayModeAudioTranslation
		} else if strings.HasPrefix(c.Request.URL.Path, "/v1/audio/transcriptions") {
			modelRequest.Model = common.GetStringIfEmpty(modelRequest.Model, c.PostForm("model"))
			modelRequest.Model = common.GetStringIfEmpty(modelRequest.Model, "whisper-1")
			relayMode = relayconstant.RelayModeAudioTranscription
		}
		c.Set("relay_mode", relayMode)
	}
	return &modelRequest, shouldSelectChannel, nil
}

func SetupContextForSelectedChannel(c *gin.Context, channel *model.Channel, modelName string) {
	c.Set("original_model", modelName) // for retry
	if channel == nil {
		return
	}
	c.Set("channel_id", channel.Id)
	c.Set("channel_name", channel.Name)
	c.Set("channel_type", channel.Type)
	c.Set("channel_create_time", channel.CreatedTime)
	c.Set("channel_setting", channel.GetSetting())
	c.Set("param_override", channel.GetParamOverride())
	if nil != channel.OpenAIOrganization && "" != *channel.OpenAIOrganization {
		c.Set("channel_organization", *channel.OpenAIOrganization)
	}
	c.Set("auto_ban", channel.GetAutoBan())
	c.Set("model_mapping", channel.GetModelMapping())
	c.Set("status_code_mapping", channel.GetStatusCodeMapping())
	c.Request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", channel.Key))
	c.Set("base_url", channel.GetBaseURL())
	// TODO: api_version统一
	switch channel.Type {
	case common.ChannelTypeAzure:
		c.Set("api_version", channel.Other)
	case common.ChannelTypeVertexAi:
		c.Set("region", channel.Other)
	case common.ChannelTypeXunfei:
		c.Set("api_version", channel.Other)
	case common.ChannelTypeGemini:
		c.Set("api_version", channel.Other)
	case common.ChannelTypeAli:
		c.Set("plugin", channel.Other)
	case common.ChannelCloudflare:
		c.Set("api_version", channel.Other)
	case common.ChannelTypeMokaAI:
		c.Set("api_version", channel.Other)
	case common.ChannelTypeCoze:
		c.Set("bot_id", channel.Other)
	}
}

// extractModelNameFromGeminiPath 从 Gemini API URL 路径中提取模型名
// 输入格式: /v1beta/models/gemini-2.0-flash:generateContent
// 输出: gemini-2.0-flash
func extractModelNameFromGeminiPath(path string) string {
	// 查找 "/models/" 的位置
	modelsPrefix := "/models/"
	modelsIndex := strings.Index(path, modelsPrefix)
	if modelsIndex == -1 {
		return ""
	}

	// 从 "/models/" 之后开始提取
	startIndex := modelsIndex + len(modelsPrefix)
	if startIndex >= len(path) {
		return ""
	}

	// 查找 ":" 的位置，模型名在 ":" 之前
	colonIndex := strings.Index(path[startIndex:], ":")
	if colonIndex == -1 {
		// 如果没有找到 ":"，返回从 "/models/" 到路径结尾的部分
		return path[startIndex:]
	}

	// 返回模型名部分
	return path[startIndex : startIndex+colonIndex]
}
