package controller

import (
	"net/http"
	"one-api/model"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// GetAllChannelSources 获取所有渠道来源，支持分页和关键词搜索
func GetAllChannelSources(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c<PERSON>("page_size", "10"))
	if pageSize < 1 {
		pageSize = 10
	}

	keyword := c.Query("keyword")

	// 创建查询参数
	query := &model.ChannelSourceQuery{
		Page:     page,
		PageSize: pageSize,
		Keyword:  keyword,
	}

	// 获取数据
	channelSources, total, err := model.GetAllChannelSources(query)
	if err != nil {
		c.<PERSON>(http.StatusOK, gin.H{
			"success": false,
			"message": err.<PERSON><PERSON><PERSON>(),
		})
		return
	}

	// 格式化数据
	var formattedSources []gin.H
	for _, source := range channelSources {
		formattedSources = append(formattedSources, gin.H{
			"id":            source.Id,
			"channel_name":  source.ChannelName,
			"visit_count":   source.VisitCount,
			"first_visit":   time.Unix(source.FirstVisitAt, 0).Format("2006-01-02 15:04:05"),
			"last_visit":    time.Unix(source.LastVisitAt, 0).Format("2006-01-02 15:04:05"),
			"created_at":    source.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    formattedSources,
		"total":   total,
		"page":    page,
		"size":    pageSize,
	})
}

// DeleteChannelSource 删除指定的渠道来源
func DeleteChannelSource(c *gin.Context) {
	id := c.Param("id")
	err := model.DB.Delete(&model.ChannelSource{}, id).Error
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}