package service

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"one-api/dto"
	"one-api/setting"
	"one-api/common"
	"strings"
	"time"
	"one-api/model"
)

// WebhookPayload webhook 通知的负载数据
type WebhookPayload struct {
	Type      string        `json:"type"`
	Title     string        `json:"title"`
	Content   string        `json:"content"`
	Values    []interface{} `json:"values,omitempty"`
	Timestamp int64         `json:"timestamp"`
}

// generateSignature 生成 webhook 签名
func generateSignature(secret string, payload []byte) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(payload)
	return hex.EncodeToString(h.Sum(nil))
}

// SendWebhookNotify 发送 webhook 通知
func SendWebhookNotify(webhookURL string, secret string, data dto.Notify) error {
	// 处理占位符
	content := data.Content
	for _, value := range data.Values {
		content = strings.Replace(content, dto.ContentValueParam, fmt.Sprintf("%v", value), 1)
	}

	// 检查是否是飞书webhook
	isFeishu := strings.Contains(strings.ToLower(webhookURL), "feishu.cn") ||
		strings.Contains(strings.ToLower(webhookURL), "lark")

	var payloadBytes []byte
	var err error

	if isFeishu {
		// 构建飞书机器人格式的负载
		feishuPayload := map[string]interface{}{
			"msg_type": "text",
			"content": map[string]interface{}{
				"text": fmt.Sprintf("%s\n%s", data.Title, content),
			},
		}
		common.SysLog(fmt.Sprintf("使用飞书格式发送webhook: %+v", feishuPayload))
		payloadBytes, err = json.Marshal(feishuPayload)
	} else {
		// 构建标准 webhook 负载
		payload := WebhookPayload{
			Type:      data.Type,
			Title:     data.Title,
			Content:   content,
			Values:    data.Values,
			Timestamp: time.Now().Unix(),
		}
		payloadBytes, err = json.Marshal(payload)
	}

	if err != nil {
		return fmt.Errorf("failed to marshal webhook payload: %v", err)
	}

	// 创建 HTTP 请求
	var req *http.Request
	var resp *http.Response

	if setting.EnableWorker() {
		// 构建worker请求数据
		workerReq := &WorkerRequest{
			URL:    webhookURL,
			Key:    setting.WorkerValidKey,
			Method: http.MethodPost,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: payloadBytes,
		}

		// 如果有secret，添加签名到headers
		if secret != "" {
			signature := generateSignature(secret, payloadBytes)
			workerReq.Headers["X-Webhook-Signature"] = signature
			workerReq.Headers["Authorization"] = "Bearer " + secret
		}

		resp, err = DoWorkerRequest(workerReq)
		if err != nil {
			return fmt.Errorf("failed to send webhook request through worker: %v", err)
		}

		// 读取响应体
		respBody, _ := io.ReadAll(resp.Body)
		resp.Body.Close() // 手动关闭而不是使用defer

		// 检查响应状态
		if resp.StatusCode < 200 || resp.StatusCode >= 300 {
			errMsg := fmt.Sprintf("webhook request failed with status code: %d, response: %s",
				resp.StatusCode, string(respBody))
			common.SysError(errMsg)
			return fmt.Errorf(errMsg)
		}

		// 记录成功响应
		common.SysLog(fmt.Sprintf("webhook sent successfully, response: %s", string(respBody)))
	} else {
		req, err = http.NewRequest(http.MethodPost, webhookURL, bytes.NewBuffer(payloadBytes))
		if err != nil {
			return fmt.Errorf("failed to create webhook request: %v", err)
		}

		// 设置请求头
		req.Header.Set("Content-Type", "application/json")

		// 如果有 secret，生成签名
		if secret != "" {
			signature := generateSignature(secret, payloadBytes)
			req.Header.Set("X-Webhook-Signature", signature)
		}

		// 发送请求
		client := GetImpatientHttpClient()
		resp, err = client.Do(req)
		if err != nil {
			return fmt.Errorf("failed to send webhook request: %v", err)
		}

		// 读取响应体
		respBody, _ := io.ReadAll(resp.Body)
		resp.Body.Close() // 手动关闭而不是使用defer

		// 检查响应状态
		if resp.StatusCode < 200 || resp.StatusCode >= 300 {
			errMsg := fmt.Sprintf("webhook request failed with status code: %d, response: %s",
				resp.StatusCode, string(respBody))
			common.SysError(errMsg)
			return fmt.Errorf(errMsg)
		}

		// 记录成功响应
		common.SysLog(fmt.Sprintf("webhook sent successfully, response: %s", string(respBody)))
	}

	return nil
}

// SendRegisterWebhook 发送用户注册 webhook 通知
func SendRegisterWebhook(username string, userId int) error {
	// 添加调试日志
	common.SysLog(fmt.Sprintf("Attempting to send register webhook, enabled: %v, url: %s", common.RegisterWebhookEnabled, common.RegisterWebhookUrl))

	if !common.RegisterWebhookEnabled || common.RegisterWebhookUrl == "" {
		common.SysLog("Register webhook is disabled or URL is empty")
		return nil
	}

	// 生成一个更友好的消息内容
	content := fmt.Sprintf("新用户已注册到系统\n用户名：%s\n用户ID：%d\n注册时间：%s",
		username,
		userId,
		time.Now().Format("2006-01-02 15:04:05"))

	data := dto.NewNotify(
		dto.NotifyTypeUserRegister,
		"新用户注册通知",
		content,
		[]interface{}{},
	)

	common.SysLog(fmt.Sprintf("Sending register webhook notification to: %s", common.RegisterWebhookUrl))
	return SendWebhookNotify(common.RegisterWebhookUrl, common.RegisterWebhookSecret, data)
}

// SendPaymentSuccessWebhook 发送支付成功 webhook 通知
func SendPaymentSuccessWebhook(userId int, tradeNo string, amount int64, money float64) error {
	// 添加调试日志
	common.SysLog(fmt.Sprintf("Attempting to send payment success webhook, enabled: %v, url: %s",
		common.PaymentWebhookEnabled, common.PaymentWebhookUrl))

	if !common.PaymentWebhookEnabled || common.PaymentWebhookUrl == "" {
		common.SysLog("Payment webhook is disabled or URL is empty")
		return nil
	}

	// 获取用户信息
	user, err := model.GetUserById(userId, false)
	if err != nil {
		common.SysError(fmt.Sprintf("Failed to get user info for payment webhook: %s", err.Error()))
		return err
	}

	// 生成一个更友好的消息内容
	content := fmt.Sprintf("用户充值成功\n用户名：%s\n用户ID：%d\n交易号：%s\n充值数量：%d\n支付金额：%.2f\n充值时间：%s",
		user.Username,
		userId,
		tradeNo,
		amount,
		money,
		time.Now().Format("2006-01-02 15:04:05"))

	data := dto.NewNotify(
		dto.NotifyTypePaymentSuccess,
		"充值成功通知",
		content,
		[]interface{}{},
	)

	common.SysLog(fmt.Sprintf("Sending payment success webhook notification to: %s", common.PaymentWebhookUrl))
	return SendWebhookNotify(common.PaymentWebhookUrl, common.PaymentWebhookSecret, data)
}
