#!/bin/bash
#
# 简单的 Docker Compose 启动脚本
#

echo "正在启动 Docker Compose 服务..."

# 检测并使用可用的 Docker Compose 命令
if command -v docker-compose &>/dev/null; then
    COMPOSE_CMD="docker-compose"
elif docker compose version &>/dev/null; then
    COMPOSE_CMD="docker compose"
else
    echo "错误: Docker Compose 未安装"
    exit 1
fi

echo "使用您提供的凭证登录 Docker..."
echo "mxyhi123" | docker login -u mxyhi123 --password-stdin docker.mxyhi.com

echo "使用 --build 参数重新构建镜像..."
$COMPOSE_CMD -f ./docker-compose_prod.yml pull
$COMPOSE_CMD -f ./docker-compose_prod.yml up --build -d

echo "服务已启动"
