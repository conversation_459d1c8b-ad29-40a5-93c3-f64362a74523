#!/bin/bash

# Define image name and tag
IMAGE_NAME="new-api"
IMAGE_TAG="latest"
REMOTE_REPO="docker.mxyhi.com"

# Full image name for the remote repository
FULL_IMAGE_NAME="$REMOTE_REPO/$IMAGE_NAME:$IMAGE_TAG"
LOCAL_IMAGE_NAME="$IMAGE_NAME:$IMAGE_TAG"

# Build the Docker image
echo "Building Docker image: $LOCAL_IMAGE_NAME"
docker build -t $LOCAL_IMAGE_NAME .

# Check if the build was successful
if [ $? -ne 0 ]; then
    echo "Docker build failed!"
    exit 1
fi

# Tag the image for the remote repository
echo "Tagging image $LOCAL_IMAGE_NAME as $FULL_IMAGE_NAME"
docker tag $LOCAL_IMAGE_NAME $FULL_IMAGE_NAME

# Check if tagging was successful
if [ $? -ne 0 ]; then
    echo "Docker tag failed!"
    exit 1
fi

# Login to Docker Hub
# WARNING: Hardcoding credentials is a security risk. Consider using environment variables or Docker credential helpers.
echo "Logging in to $REMOTE_REPO"
if [ -z "$DOCKER_USERNAME" ] || [ -z "$DOCKER_PASSWORD" ]; then
    echo "Error: DOCKER_USERNAME and DOCKER_PASSWORD environment variables are not set."
    echo "Attempting login with credentials from build-docker.sh (this is insecure for CI)."
    docker login $REMOTE_REPO -u "mxyhi123" -p "mxyhi123"
else
    docker login $REMOTE_REPO -u "$DOCKER_USERNAME" -p "$DOCKER_PASSWORD"
fi

# Check if login was successful
if [ $? -ne 0 ]; then
    echo "Docker login failed!"
    exit 1
fi

# Push the image to the remote repository
echo "Pushing image $FULL_IMAGE_NAME to $REMOTE_REPO"
docker push $FULL_IMAGE_NAME

# Check if the push was successful
if [ $? -ne 0 ]; then
    echo "Docker push failed!"
    exit 1
fi

echo "Docker image pushed successfully:  $FULL_IMAGE_NAME"
