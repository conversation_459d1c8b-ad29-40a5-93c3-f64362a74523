<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  </head>
  <body>
    <div
      class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mt-8 max-w-xl mx-auto rounded shadow"
    >
      <p class="font-bold text-lg">限时优惠</p>
      <p class="mt-2 text-base">￥4.2 = $1</p>
      <p class="mt-2 text-base">单次充值 $30 及以上 ￥4.1 = $1</p>
      <p class="mt-2 text-base">单次充值 $100 及以上 ￥4.0 = $1</p>
      <p class="mt-2 text-base text-green-700">相比官方便宜6折左右</p>
    </div>
    <div
      class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mt-8 max-w-xl mx-auto rounded shadow"
    >
      <p class="font-bold text-lg">售后服务</p>
      <p class="mt-2 text-base">QQ售后群：982616138</p>
    </div>
    <div
      class="max-w-xl mx-auto mt-8 p-4 bg-white rounded shadow border border-gray-200"
    >
      <label for="rmbInput" class="block mb-2 font-medium text-gray-700"
        >计算实际到账：</label
      >
      <div class="flex items-center space-x-2">
        <span class="text-gray-600">￥</span>
        <input
          id="rmbInput"
          type="number"
          min="0"
          step="0.01"
          placeholder="请输入充值的人民币金额"
          class="border rounded px-3 py-1 w-40 focus:outline-none focus:ring-2 focus:ring-yellow-400"
        />
        <span class="mx-2" id="rateDisplay">/ 4.2 =</span>
        <span class="text-green-700 font-bold" id="usdResult">$0.00</span>
      </div>
    </div>
    <div
      class="max-w-xl mx-auto mt-4 p-4 bg-white rounded shadow border border-gray-200"
    >
      <label for="rmbQuotaInput" class="block mb-2 font-medium text-gray-700"
        >计算实际额度：</label
      >
      <div class="flex items-center space-x-2">
        <span class="text-gray-600">￥</span>
        <input
          id="rmbQuotaInput"
          type="number"
          min="0"
          step="0.01"
          placeholder="输入人民币金额"
          class="border rounded px-3 py-1 w-40 focus:outline-none focus:ring-2 focus:ring-blue-400"
        />
        <span class="text-gray-600">/ </span>
        <span id="quotaDynamicRateDisplay" class="text-gray-700 font-medium"
          >4.2</span
        >
        <span class="text-gray-600"> * 500,000 =</span>
        <span class="text-blue-700 font-bold" id="quotaResultIndependentDisplay"
          >0</span
        >
      </div>
    </div>
    <div
      class="max-w-xl mx-auto mt-4 p-4 bg-white rounded shadow border border-gray-200"
    >
      <label for="usdDirectInput" class="block mb-2 font-medium text-gray-700"
        >计算美元对应额度：</label
      >
      <div class="flex items-center space-x-2">
        <span class="text-gray-600">$</span>
        <input
          id="usdDirectInput"
          type="number"
          min="0"
          step="0.01"
          placeholder="输入美元金额"
          class="border rounded px-3 py-1 w-40 focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <span class="text-gray-600"> * 500,000 =</span>
        <span class="text-purple-700 font-bold" id="directUsdQuotaResult"
          >0</span
        >
      </div>
    </div>
    <script>
      const rmbInput = document.getElementById("rmbInput");
      const usdResult = document.getElementById("usdResult");
      const rateDisplay = document.getElementById("rateDisplay");

      function getRate(usd) {
        if (usd >= 100) return 4.0;
        if (usd >= 30) return 4.1;
        return 4.2;
      }

      rmbInput.addEventListener("input", function () {
        const rmb = parseFloat(rmbInput.value);
        let usd = 0;
        let rate = 4.2;
        let usdForRateTier = 0;

        if (!isNaN(rmb) && rmb > 0) {
          usdForRateTier = rmb / 4.2;
          rate = getRate(usdForRateTier);
          usd = rmb / rate;
        }
        rateDisplay.textContent = `/ ${rate.toFixed(1)} =`;
        usdResult.textContent = `$${usd.toFixed(2)}`;
      });

      // New independent section for Quota Calculation
      const rmbQuotaInput = document.getElementById("rmbQuotaInput");
      const quotaResultIndependentDisplay = document.getElementById(
        "quotaResultIndependentDisplay"
      );
      const quotaDynamicRateDisplay = document.getElementById(
        "quotaDynamicRateDisplay"
      );

      rmbQuotaInput.addEventListener("input", function () {
        const rmb = parseFloat(rmbQuotaInput.value);
        let quota = 0;
        let currentRate = 4.2;
        let usdForRateTier = 0;

        if (!isNaN(rmb) && rmb > 0) {
          usdForRateTier = rmb / 4.2; // Determine initial USD to find the rate tier
          currentRate = getRate(usdForRateTier);
          quota = (rmb / currentRate) * 500000;
        }
        quotaDynamicRateDisplay.textContent = currentRate.toFixed(1);
        const formattedQuota = quota.toLocaleString(undefined, {
          maximumFractionDigits: 0,
        });
        quotaResultIndependentDisplay.textContent = formattedQuota;
      });

      quotaResultIndependentDisplay.addEventListener("click", function () {
        const textToCopy = this.textContent;
        navigator.clipboard
          .writeText(textToCopy.replace(/,/g, ""))
          .then(() => {
            const originalText = this.textContent;
            this.textContent = "已复制!";
            this.classList.add("text-green-500");
            setTimeout(() => {
              this.textContent = originalText;
              this.classList.remove("text-green-500");
            }, 1500);
          })
          .catch((err) => {
            console.error("无法复制文本: ", err);
          });
      });

      // New independent section for USD to Quota calculation
      const usdDirectInput = document.getElementById("usdDirectInput");
      const directUsdQuotaResult = document.getElementById(
        "directUsdQuotaResult"
      );

      usdDirectInput.addEventListener("input", function () {
        const usdValue = parseFloat(usdDirectInput.value);
        let quota = 0;
        if (!isNaN(usdValue) && usdValue > 0) {
          quota = usdValue * 500000;
        }
        const formattedDirectQuota = quota.toLocaleString(undefined, {
          maximumFractionDigits: 0,
        });
        directUsdQuotaResult.textContent = formattedDirectQuota;
      });

      directUsdQuotaResult.addEventListener("click", function () {
        const textToCopy = this.textContent;
        navigator.clipboard
          .writeText(textToCopy.replace(/,/g, ""))
          .then(() => {
            const originalText = this.textContent;
            this.textContent = "已复制!";
            this.classList.add("text-green-500");
            setTimeout(() => {
              this.textContent = originalText;
              this.classList.remove("text-green-500");
            }, 1500);
          })
          .catch((err) => {
            console.error("无法复制文本: ", err);
          });
      });
    </script>
  </body>
</html>
