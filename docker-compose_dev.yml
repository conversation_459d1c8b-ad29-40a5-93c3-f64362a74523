services:
  new-api:
    # image: calciumion/new-api:latest
    build: .
    restart: always
    command: --log-dir /app/logs
    ports:
      - "8066:3000"
    volumes:
      - ./data:/data
      - ./logs:/app/logs
    environment:
      - SQL_DSN=root:123456@tcp(mysql:3306)/new-api # 指向mysql服务
      - REDIS_CONN_STRING=redis://redis
      - TZ=Asia/Shanghai
      - GENERATE_DEFAULT_TOKEN=true
      - DEFAULT_QUOTA=10
    #      - SESSION_SECRET=random_string  # 多机部署时设置，必须修改这个随机字符串！！！！！！！
    #      - NODE_TYPE=slave  # 多机部署的从节点取消注释
    #      - SYNC_FREQUENCY=60  # 如需定期同步数据库，取消注释
    #      - FRONTEND_BASE_URL=https://your-domain.com  # 多机部署带前端URL时取消注释

    depends_on:
      - redis
      - mysql
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "wget -q -O - http://localhost:3000/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $$2}'",
        ]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:latest
    restart: always

  mysql:
    image: mysql:8.2
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456 # 确保与SQL_DSN中的密码一致
      MYSQL_DATABASE: new-api
    volumes:
      - newapi_mysql_data:/var/lib/mysql
    # ports:
    #   - "3306:3306"  # 如需从Docker外部访问MySQL，取消注释

volumes:
  newapi_mysql_data:
