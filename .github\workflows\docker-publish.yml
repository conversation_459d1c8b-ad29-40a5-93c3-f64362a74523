name: Dock<PERSON> PublishAdd commentMore actions

on:
  push:
    branches:
      - main

jobs:
  build_and_push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        env:
          DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
        run: |
          echo "Logging in to docker.mxyhi.com"
          if [ -z "$DOCKER_USERNAME" ] || [ -z "$DOCKER_PASSWORD" ]; then
            echo "Error: DOCKER_USERNAME and/or DOCKER_PASSWORD secrets are not set in GitHub repository settings."
            exit 1
          fi
          echo $DOCKER_PASSWORD | docker login docker.mxyhi.com -u $DOCKER_USERNAME --password-stdin

      - name: Make build script executable
        run: chmod +x ./build-docker.sh

      - name: Run build and push script
        run: ./build-docker.sh
