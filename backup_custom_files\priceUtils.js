import Big from 'big.js';

// 设置 Big.js 的默认配置
Big.DP = 20; // 设置最大小数位数
Big.RM = Big.roundHalfUp; // 设置四舍五入模式

/**
 * 格式化价格，去除多余的小数0，使用四舍五入
 * @param {number|string|Big} value - 要格式化的价格值
 * @param {number} decimalPlaces - 最大保留的小数位数，默认为2
 * @returns {string} 格式化后的价格字符串
 */
export function formatPrice(value, decimalPlaces = 2) {
  try {
    if (value === null || value === undefined || value === '') {
      return '0';
    }

    const bigValue = new Big(value);
    const rounded = bigValue.round(decimalPlaces, Big.roundHalfUp);

    // 转换为字符串并去除末尾的零
    let result = rounded.toString();

    // 如果是整数，直接返回
    if (result.indexOf('.') === -1) {
      return result;
    }

    // 去除末尾的零
    result = result.replace(/\.?0+$/, '');

    // 如果结果为空字符串，返回'0'
    return result || '0';
  } catch (error) {
    console.error('Price formatting error:', error);
    return '0';
  }
}

/**
 * 精确的价格计算 - 加法
 * @param {number|string|Big} a
 * @param {number|string|Big} b
 * @returns {Big} Big数字对象
 */
export function addPrice(a, b) {
  try {
    return new Big(a || 0).plus(new Big(b || 0));
  } catch (error) {
    console.error('Price addition error:', error);
    return new Big(0);
  }
}

/**
 * 精确的价格计算 - 减法
 * @param {number|string|Big} a
 * @param {number|string|Big} b
 * @returns {Big} Big数字对象
 */
export function subtractPrice(a, b) {
  try {
    return new Big(a || 0).minus(new Big(b || 0));
  } catch (error) {
    console.error('Price subtraction error:', error);
    return new Big(0);
  }
}

/**
 * 精确的价格计算 - 乘法
 * @param {number|string|Big} a
 * @param {number|string|Big} b
 * @returns {Big} Big数字对象
 */
export function multiplyPrice(a, b) {
  try {
    return new Big(a || 0).times(new Big(b || 0));
  } catch (error) {
    console.error('Price multiplication error:', error);
    return new Big(0);
  }
}

/**
 * 精确的价格计算 - 除法
 * @param {number|string|Big} a
 * @param {number|string|Big} b
 * @returns {Big} Big数字对象
 */
export function dividePrice(a, b) {
  try {
    const divisor = new Big(b || 1);
    if (divisor.eq(0)) {
      console.error('Division by zero error');
      return new Big(0);
    }
    return new Big(a || 0).div(divisor);
  } catch (error) {
    console.error('Price division error:', error);
    return new Big(0);
  }
}

/**
 * 格式化价格显示，当小数超过三位时四舍五入保留两位小数
 * @param {number|string|Big} value - 价格值
 * @param {number} maxDecimalPlaces - 最大显示小数位数，默认为2
 * @returns {string} 格式化后的价格字符串
 */
export function formatPriceDisplay(value, maxDecimalPlaces = 2) {
  try {
    if (value === null || value === undefined || value === '') {
      return '0.00';
    }

    const bigValue = new Big(value);
    const valueStr = bigValue.toString();

    // 检查是否有小数部分
    const decimalIndex = valueStr.indexOf('.');
    if (decimalIndex === -1) {
      // 没有小数部分，添加.00
      return valueStr + '.00';
    }

    const decimalPart = valueStr.substring(decimalIndex + 1);

    // 如果小数位数超过maxDecimalPlaces，进行四舍五入
    if (decimalPart.length > maxDecimalPlaces) {
      return bigValue
        .round(maxDecimalPlaces, Big.roundHalfUp)
        .toFixed(maxDecimalPlaces);
    }

    // 如果小数位数不足maxDecimalPlaces，补零
    return bigValue.toFixed(maxDecimalPlaces);
  } catch (error) {
    console.error('Price display formatting error:', error);
    return '0.00';
  }
}

/**
 * 比较两个价格的大小
 * @param {number|string|Big} a
 * @param {number|string|Big} b
 * @returns {number} 1: a > b, 0: a = b, -1: a < b
 */
export function comparePrice(a, b) {
  try {
    return new Big(a || 0).cmp(new Big(b || 0));
  } catch (error) {
    console.error('Price comparison error:', error);
    return 0;
  }
}

/**
 * 检查价格是否为零
 * @param {number|string|Big} value
 * @returns {boolean}
 */
export function isPriceZero(value) {
  try {
    return new Big(value || 0).eq(0);
  } catch (error) {
    console.error('Price zero check error:', error);
    return true;
  }
}

/**
 * 将价格转换为数字（谨慎使用，可能丢失精度）
 * @param {number|string|Big} value
 * @returns {number}
 */
export function priceToNumber(value) {
  try {
    return new Big(value || 0).toNumber();
  } catch (error) {
    console.error('Price to number conversion error:', error);
    return 0;
  }
}
