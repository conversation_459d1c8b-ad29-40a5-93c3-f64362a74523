import React from 'react';
import { Layout, Typography, Button, Table, Input, Space, Banner, Pagination } from '@douyinfe/semi-ui';
import { API, showError, showSuccess } from '../../helpers';
import { useTranslation } from 'react-i18next';
import { IconSearch } from '@douyinfe/semi-icons';

const { Content } = Layout;
const { Title, Text } = Typography;

// 点击来源页面 - 按照系统风格设计
class ChannelSource extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      dataSource: [],
      searchText: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
    };
  }

  componentDidMount() {
    this.fetchData();
  }

  fetchData = () => {
    const { currentPage, pageSize, searchText } = this.state;
    this.setState({ loading: true });

    // 构建查询参数
    const params = {
      page: currentPage,
      page_size: pageSize,
    };

    // 如果有搜索文本，添加到查询参数
    if (searchText) {
      params.keyword = searchText;
    }

    API.get('/api/channel_source/', { params })
      .then(res => {
        if (res && res.data && res.data.success) {
          // 添加更强健的数据验证
          let sourceData = res.data.data || [];
          const total = res.data.total || 0;

          if (!Array.isArray(sourceData)) {
            console.error('API返回的数据不是数组:', sourceData);
            sourceData = [];
          }

          this.setState({
            dataSource: sourceData,
            total: total
          });
        } else {
          showError(res?.data?.message || '加载数据失败');
          this.setState({
            dataSource: [],
            total: 0
          });
        }
      })
      .catch(err => {
        console.error('加载失败:', err);
        showError('加载数据失败');
        this.setState({
          dataSource: [],
          total: 0
        });
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  }

  handleDelete = (id) => {
    if (!id) return;

    API.delete(`/api/channel_source/${id}`)
      .then(res => {
        if (res && res.data && res.data.success) {
          showSuccess('删除成功');
          this.fetchData();
        } else {
          showError(res?.data?.message || '删除失败');
        }
      })
      .catch(err => {
        console.error('删除失败:', err);
        showError('删除失败');
      });
  }

  handleSearch = (value) => {
    this.setState({
      searchText: value,
      currentPage: 1 // 搜索时重置到第一页
    }, () => {
      this.fetchData();
    });
  }

  handlePageChange = (page, pageSize) => {
    this.setState({
      currentPage: page,
      pageSize: pageSize
    }, () => {
      this.fetchData();
    });
  }

  render() {
    const { dataSource, loading, searchText, currentPage, pageSize, total } = this.state;

    const columns = [
      { title: 'ID', dataIndex: 'id' },
      { title: '来源名称', dataIndex: 'channel_name' },
      { title: '访问次数', dataIndex: 'visit_count' },
      { title: '首次访问', dataIndex: 'first_visit' },
      { title: '最近访问', dataIndex: 'last_visit' },
      { title: '创建时间', dataIndex: 'created_at' },
      {
        title: '操作',
        render: (text, record) => (
          <Button
            type="danger"
            size="small"
            onClick={() => {
              if (window.confirm('确定要删除吗？')) {
                this.handleDelete(record.id);
              }
            }}
          >
            删除
          </Button>
        )
      }
    ];

    return (
      <Layout>
        <Layout.Header>
          <Banner
            type='info'
            description='记录用户通过GET方式访问网站时URL中的_channel参数值'
            closeIcon={null}
          />
        </Layout.Header>
        <Layout.Content style={{ padding: '16px 0' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px', padding: '0 16px' }}>
            <Title heading={3}>点击来源管理</Title>
            <Space>
              <Input
                prefix={<IconSearch />}
                placeholder="搜索来源名称"
                value={searchText}
                onChange={this.handleSearch}
                showClear
                style={{ width: 200 }}
                onEnterPress={() => this.fetchData()}
              />
              <Button type="primary" onClick={this.fetchData} loading={loading}>
                刷新
              </Button>
            </Space>
          </div>

          <Table
            loading={loading}
            columns={columns}
            dataSource={dataSource || []}
            pagination={false}
            empty="暂无数据"
            rowKey="id"
            onRow={(record) => ({
              onClick: () => {
                console.log('Row clicked:', record);
              }
            })}
          />

          <div style={{ padding: '16px', display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
            <Text style={{ marginRight: '16px' }}>共 {total} 条</Text>
            <Pagination
              currentPage={currentPage}
              pageSize={pageSize}
              total={total}
              onPageChange={this.handlePageChange}
              showSizeChanger
              pageSizeOpts={[10, 20, 50, 100]}
            />
          </div>
        </Layout.Content>
      </Layout>
    );
  }
}

export default ChannelSource;