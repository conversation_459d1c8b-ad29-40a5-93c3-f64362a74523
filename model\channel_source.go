package model

import (
	"time"

	"gorm.io/gorm"
)

// ChannelSource 存储用户通过GET请求访问时的渠道来源参数
type ChannelSource struct {
	Id           int            `json:"id" gorm:"primaryKey"`
	ChannelName  string         `json:"channel_name" gorm:"type:varchar(255);index"` // 渠道名称（_channel参数值）
	VisitCount   int            `json:"visit_count" gorm:"type:int;default:0"`       // 访问次数
	FirstVisitAt int64          `json:"first_visit_at" gorm:"type:bigint"`           // 首次访问时间
	LastVisitAt  int64          `json:"last_visit_at" gorm:"type:bigint"`            // 最近访问时间
	CreatedAt    time.Time      `json:"created_at"`                                  // 记录创建时间
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index"`                     // 软删除时间
}

// ChannelSourceQuery 查询参数结构体
type ChannelSourceQuery struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Keyword  string `json:"keyword"`
}

// GetAllChannelSources 获取所有渠道来源，支持分页和关键词搜索
func GetAllChannelSources(query *ChannelSourceQuery) ([]*ChannelSource, int64, error) {
	var channelSources []*ChannelSource
	var count int64

	// 创建数据库查询
	db := DB.Model(&ChannelSource{})

	// 添加搜索条件
	if query != nil && query.Keyword != "" {
		db = db.Where("channel_name LIKE ?", "%"+query.Keyword+"%")
	}

	// 先获取总记录数
	err := db.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	// 添加分页和排序
	if query != nil && query.PageSize > 0 {
		offset := (query.Page - 1) * query.PageSize
		if offset < 0 {
			offset = 0
		}
		db = db.Offset(offset).Limit(query.PageSize)
	}

	// 执行查询并排序
	err = db.Order("visit_count desc").Find(&channelSources).Error
	return channelSources, count, err
}

// GetChannelSourceByName 根据渠道名称获取渠道来源
func GetChannelSourceByName(channelName string) (*ChannelSource, error) {
	var channelSource ChannelSource
	err := DB.Where("channel_name = ?", channelName).First(&channelSource).Error
	return &channelSource, err
}

// RecordChannelSource 记录渠道来源访问
func RecordChannelSource(channelName string) error {
	now := time.Now()
	timestamp := now.Unix()

	var channelSource ChannelSource
	// 尝试查找现有记录
	result := DB.Where("channel_name = ?", channelName).First(&channelSource)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 创建新记录
			newSource := ChannelSource{
				ChannelName:  channelName,
				VisitCount:   1,
				FirstVisitAt: timestamp,
				LastVisitAt:  timestamp,
				CreatedAt:    now,
			}
			return DB.Create(&newSource).Error
		}
		return result.Error
	}

	// 更新现有记录
	channelSource.VisitCount++
	channelSource.LastVisitAt = timestamp
	return DB.Save(&channelSource).Error
}